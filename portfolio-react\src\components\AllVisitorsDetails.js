import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaGlobe, FaClock, FaEye, FaUsers, FaMapMarkerAlt } from 'react-icons/fa';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { formatDuration } from '../hooks/useVisitorTracking';
import { batchGetCountriesFromIPs } from '../utils/geolocation';
import './AllVisitorsDetails.css';

const AllVisitorsDetails = () => {
  const [visitorsData, setVisitorsData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [geoLoading, setGeoLoading] = useState(false);
  const [sortBy, setSortBy] = useState('totalTime'); // totalTime, visits, country, lastVisit
  const [filterCountry, setFilterCountry] = useState('all');
  const navigate = useNavigate();

  useEffect(() => {
    const fetchAllVisitorsData = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No token found. Please log in.');
        navigate('/admin/login');
        return;
      }

      // Wake up backend before fetching data
      await preemptiveWakeup();

      try {
        const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/dashboard`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Process visitor data by IP
        const visitorMap = new Map();
        
        data.visits.forEach(visit => {
          if (!visitorMap.has(visit.ip)) {
            visitorMap.set(visit.ip, {
              ip: visit.ip,
              visits: [],
              totalTime: 0,
              sectionsVisited: new Set(),
              sectionStats: {},
              firstVisit: new Date(visit.timestamp),
              lastVisit: new Date(visit.timestamp)
            });
          }
          
          const visitor = visitorMap.get(visit.ip);
          visitor.visits.push(visit);
          visitor.totalTime += visit.duration || 0;
          visitor.sectionsVisited.add(visit.section);
          
          // Update first and last visit times
          const visitTime = new Date(visit.timestamp);
          if (visitTime < visitor.firstVisit) visitor.firstVisit = visitTime;
          if (visitTime > visitor.lastVisit) visitor.lastVisit = visitTime;
          
          // Update section statistics
          if (!visitor.sectionStats[visit.section]) {
            visitor.sectionStats[visit.section] = {
              count: 0,
              totalDuration: 0
            };
          }
          visitor.sectionStats[visit.section].count++;
          visitor.sectionStats[visit.section].totalDuration += visit.duration || 0;
        });
        
        // Convert to array and add derived properties
        const processedVisitors = Array.from(visitorMap.values()).map(visitor => ({
          ...visitor,
          sectionsVisited: Array.from(visitor.sectionsVisited),
          visitCount: visitor.visits.length,
          avgSessionTime: visitor.totalTime / visitor.visits.length,
          mostVisitedSection: Object.entries(visitor.sectionStats)
            .sort(([,a], [,b]) => b.totalDuration - a.totalDuration)[0]?.[0] || 'N/A'
        }));
        
        setVisitorsData(processedVisitors);

        // Fetch geolocation data for all IPs (with error handling)
        setGeoLoading(true);
        try {
          const uniqueIPs = [...new Set(processedVisitors.map(v => v.ip))];
          console.log(`Fetching geolocation for ${uniqueIPs.length} unique IPs`);

          const geoData = await batchGetCountriesFromIPs(uniqueIPs);

          // Add geolocation data to visitors
          const visitorsWithGeo = processedVisitors.map(visitor => ({
            ...visitor,
            geo: geoData[visitor.ip] || {
              country: 'Unknown',
              country_code: 'UN',
              city: 'Unknown',
              flag: '🌍',
              error: true
            }
          }));

          setVisitorsData(visitorsWithGeo);
          console.log('Geolocation data successfully added to visitors');

        } catch (geoError) {
          console.error('Geolocation lookup failed:', geoError);

          // Set visitors with fallback geo data
          const visitorsWithFallbackGeo = processedVisitors.map(visitor => ({
            ...visitor,
            geo: {
              country: 'Unknown',
              country_code: 'UN',
              city: 'Unknown',
              flag: '🌍',
              error: true,
              errorMessage: 'Geolocation service unavailable'
            }
          }));

          setVisitorsData(visitorsWithFallbackGeo);
        } finally {
          setGeoLoading(false);
        }
        
      } catch (error) {
        console.error('All visitors fetch error:', error);
        setError('Error fetching visitor data: ' + error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAllVisitorsData();
  }, [navigate]);

  const handleBackToDashboard = () => {
    navigate('/admin/dashboard');
  };

  const handleVisitorClick = (ip) => {
    navigate(`/admin/visitor/${encodeURIComponent(ip)}`);
  };

  // Sort visitors based on selected criteria
  const sortedVisitors = [...visitorsData].sort((a, b) => {
    switch (sortBy) {
      case 'totalTime':
        return b.totalTime - a.totalTime;
      case 'visits':
        return b.visitCount - a.visitCount;
      case 'country':
        return (a.geo?.country || 'Unknown').localeCompare(b.geo?.country || 'Unknown');
      case 'lastVisit':
        return new Date(b.lastVisit) - new Date(a.lastVisit);
      default:
        return 0;
    }
  });

  // Filter by country if selected
  const filteredVisitors = filterCountry === 'all' 
    ? sortedVisitors 
    : sortedVisitors.filter(visitor => visitor.geo?.country === filterCountry);

  // Get unique countries for filter dropdown
  const uniqueCountries = [...new Set(visitorsData.map(v => v.geo?.country).filter(Boolean))].sort();

  if (loading) {
    return (
      <div className="all-visitors-container">
        <div className="all-visitors-loading">Loading all visitors data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="all-visitors-container">
        <div className="all-visitors-error">{error}</div>
      </div>
    );
  }

  return (
    <div className="all-visitors-container">
      <div className="all-visitors-header">
        <button onClick={handleBackToDashboard} className="back-button">
          <FaArrowLeft /> Back to Dashboard
        </button>
        <h1><FaUsers /> All Visitors Details</h1>
        {geoLoading && <div className="geo-loading">Loading location data...</div>}
      </div>

      {/* Controls */}
      <div className="visitors-controls">
        <div className="control-group">
          <label>Sort by:</label>
          <select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
            <option value="totalTime">Total Time Spent</option>
            <option value="visits">Number of Visits</option>
            <option value="country">Country</option>
            <option value="lastVisit">Last Visit</option>
          </select>
        </div>
        
        <div className="control-group">
          <label>Filter by Country:</label>
          <select value={filterCountry} onChange={(e) => setFilterCountry(e.target.value)}>
            <option value="all">All Countries</option>
            {uniqueCountries.map(country => (
              <option key={country} value={country}>{country}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="visitors-summary">
        <div className="summary-stat">
          <FaUsers /> Total Visitors: {filteredVisitors.length}
        </div>
        <div className="summary-stat">
          <FaGlobe /> Countries: {uniqueCountries.length}
        </div>
        <div className="summary-stat">
          <FaClock /> Total Time: {formatDuration(filteredVisitors.reduce((sum, v) => sum + v.totalTime, 0))}
        </div>
      </div>

      {/* Visitors Grid */}
      <div className="all-visitors-grid">
        {filteredVisitors.map((visitor, index) => (
          <div key={visitor.ip} className="visitor-card" onClick={() => handleVisitorClick(visitor.ip)}>
            <div className="visitor-card-header">
              <div className="visitor-location">
                <span className="country-flag">{visitor.geo?.flag || '🌍'}</span>
                <div className="location-info">
                  <div className="country-name">{visitor.geo?.country || 'Unknown'}</div>
                  <div className="city-name">{visitor.geo?.city || 'Unknown'}</div>
                </div>
              </div>
              <div className="visitor-ip">{visitor.ip}</div>
            </div>
            
            <div className="visitor-stats">
              <div className="stat-item">
                <FaEye className="stat-icon" />
                <span>{visitor.visitCount} visits</span>
              </div>
              <div className="stat-item">
                <FaClock className="stat-icon" />
                <span>{formatDuration(visitor.totalTime)}</span>
              </div>
              <div className="stat-item">
                <FaMapMarkerAlt className="stat-icon" />
                <span>{visitor.sectionsVisited.length} sections</span>
              </div>
            </div>
            
            <div className="visitor-details">
              <div className="most-visited">
                <strong>Most interested in:</strong> {visitor.mostVisitedSection}
              </div>
              <div className="visit-times">
                <div>First: {visitor.firstVisit.toLocaleDateString()}</div>
                <div>Last: {visitor.lastVisit.toLocaleDateString()}</div>
              </div>
            </div>
            
            <div className="visitor-card-footer">
              Click for full details →
            </div>
          </div>
        ))}
      </div>
      
      {filteredVisitors.length === 0 && (
        <div className="no-visitors">No visitors found matching the current filters.</div>
      )}
    </div>
  );
};

export default AllVisitorsDetails;
