const Admin = require('../models/Admin');
const Visit = require('../models/Visit');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');

// POST /api/admin/login
exports.login = async (req, res) => {
  // Sanitize input
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ message: 'Invalid input' });
  }
  const { email, password } = req.body;
  try {
    // Find admin in database
    const admin = await Admin.findOne({ email });
    if (!admin) {
      console.log(`[LOGIN FAIL] IP: ${req.visitorIP}, Time: ${new Date().toISOString()}, Reason: admin not found`);
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Compare password with hashed password from database
    const isMatch = await bcrypt.compare(password, admin.password);
    if (!isMatch) {
      console.log(`[LOGIN FAIL] IP: ${req.visitorIP}, Time: ${new Date().toISOString()}, Reason: wrong password`);
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const token = jwt.sign({ email }, process.env.JWT_SECRET, { expiresIn: '1h' });
    console.log(`[LOGIN SUCCESS] IP: ${req.visitorIP}, Time: ${new Date().toISOString()}, Email: ${email}`);
    res.json({ token });
  } catch (err) {
    console.log('LOGIN ERROR:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// GET /api/admin/dashboard
exports.dashboard = async (req, res) => {
  try {
    // Get total visits and unique visitors
    const totalVisits = await Visit.countDocuments();
    const uniqueVisitors = await Visit.distinct('ip').then(ips => ips.length);

    // Get recent visits with more details
    const recentVisits = await Visit.find()
      .sort({ timestamp: -1 })
      .limit(20)
      .select('ip timestamp section duration pageUrl');

    // Aggregate section stats based on total duration spent
    const sectionDurationStats = await Visit.aggregate([
      {
        $group: {
          _id: '$section',
          totalDuration: { $sum: '$duration' },
          visitCount: { $sum: 1 },
          avgDuration: { $avg: '$duration' },
          uniqueVisitors: { $addToSet: '$ip' }
        }
      },
      {
        $addFields: {
          uniqueVisitorCount: { $size: '$uniqueVisitors' }
        }
      },
      {
        $sort: { totalDuration: -1 }
      }
    ]);

    // Calculate total duration across all sections for percentage calculation
    const totalDurationAllSections = sectionDurationStats.reduce(
      (sum, section) => sum + section.totalDuration, 0
    );

    // Format section stats with duration-based percentages
    const statsWithPercent = sectionDurationStats.map(s => ({
      section: s._id,
      count: s.visitCount,
      totalDuration: Math.round(s.totalDuration),
      avgDuration: Math.round(s.avgDuration),
      uniqueVisitors: s.uniqueVisitorCount,
      percent: totalDurationAllSections > 0
        ? ((s.totalDuration / totalDurationAllSections) * 100).toFixed(1)
        : 0
    }));

    // Format recent visits for display
    const formattedVisits = recentVisits.map(v => ({
      ip: v.ip,
      timestamp: v.timestamp,
      section: v.section,
      duration: Math.round(v.duration || 0),
      pageUrl: v.pageUrl || ''
    }));

    // Calculate average session duration per unique visitor
    const sessionDurations = await Visit.aggregate([
      {
        $group: {
          _id: '$ip',
          totalSessionDuration: { $sum: '$duration' }
        }
      }
    ]);

    const avgSessionDuration = sessionDurations.length > 0
      ? Math.round(sessionDurations.reduce((sum, session) => sum + session.totalSessionDuration, 0) / sessionDurations.length)
      : 0;

    res.json({
      totalVisits,
      uniqueVisitors,
      visits: formattedVisits,
      sectionStats: statsWithPercent,
      summary: {
        totalDurationAllSections: Math.round(totalDurationAllSections),
        avgSessionDuration: avgSessionDuration
      }
    });
  } catch (err) {
    console.error('Dashboard error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};