// Backend IP Geolocation utility service
// Server-side implementation to avoid CORS issues

const https = require('https');

const GEOLOCATION_CACHE = new Map();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
const RATE_LIMIT_DELAY = 5000; // 5 seconds between requests (more conservative)
const MAX_RETRIES = 2;
let lastRequestTime = 0;

/**
 * Get country information for an IP address (Backend version)
 * @param {string} ip - IP address to lookup
 * @returns {Promise<Object>} Country information object
 */
const getCountryFromIP = async (ip) => {
  // Clean IP address - remove any comma-separated values
  if (ip && ip.includes(',')) {
    ip = ip.split(',')[0].trim();
  }

  // Check if IP is localhost or private
  if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
    return {
      country: 'Local',
      country_code: 'LO',
      city: 'Localhost',
      region: 'Local',
      flag: '🏠',
      error: false
    };
  }

  // Check cache first
  const cacheKey = ip;
  const cached = GEOLOCATION_CACHE.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    console.log(`Using cached data for IP: ${ip}`);
    return cached.data;
  }

  // Rate limiting - ensure we don't make requests too frequently
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    const waitTime = RATE_LIMIT_DELAY - timeSinceLastRequest;
    console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  lastRequestTime = Date.now();

  try {
    console.log(`Getting location for IP: ${ip}`);

    let geoData = null;
    let lastError = null;

    // Try with retries for rate limiting
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        geoData = await makeGeolocationRequest(ip);
        break; // Success, exit retry loop
      } catch (error) {
        lastError = error;

        // If it's a rate limit error and we have retries left, wait longer
        if (error.message.includes('Too many') && attempt < MAX_RETRIES) {
          const waitTime = RATE_LIMIT_DELAY * attempt; // Exponential backoff
          console.log(`Rate limited, waiting ${waitTime}ms before retry ${attempt + 1}...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
          continue;
        }

        // If it's not a rate limit error or we're out of retries, break
        break;
      }
    }

    if (!geoData) {
      throw lastError || new Error('Failed to get geolocation data');
    }

    const countryInfo = {
      country: geoData.country_name || 'Unknown',
      country_code: geoData.country_code || 'UN',
      city: geoData.city || 'Unknown',
      region: geoData.region || 'Unknown',
      timezone: geoData.timezone || 'Unknown',
      isp: geoData.org || 'Unknown',
      flag: getCountryFlag(geoData.country_code),
      error: false
    };

    // Cache the result
    GEOLOCATION_CACHE.set(cacheKey, {
      data: countryInfo,
      timestamp: Date.now()
    });

    console.log(`Successfully got location for ${ip}:`, countryInfo.country);
    return countryInfo;

  } catch (error) {
    console.warn(`Geolocation lookup failed for IP ${ip}:`, error.message);

    // Return fallback data
    const fallbackData = {
      country: 'Unknown',
      country_code: 'UN',
      city: 'Unknown',
      region: 'Unknown',
      timezone: 'Unknown',
      isp: 'Unknown',
      flag: '🌍',
      error: true,
      errorMessage: error.message
    };

    // Cache the fallback to avoid repeated failed requests (shorter cache time)
    GEOLOCATION_CACHE.set(cacheKey, {
      data: fallbackData,
      timestamp: Date.now() - (CACHE_DURATION * 0.9) // Cache for only 10% of normal time
    });

    return fallbackData;
  }
};

/**
 * Make HTTP request to geolocation API
 * @param {string} ip - IP address to lookup
 * @returns {Promise<Object>} API response data
 */
const makeGeolocationRequest = (ip) => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'ipapi.co',
      path: `/${ip}/json/`,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Portfolio-Backend/1.0'
      },
      timeout: 10000 // 10 second timeout
    };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          // Check for rate limiting first
          if (res.statusCode === 429) {
            reject(new Error('Too many requests - rate limited'));
            return;
          }

          // Check if response looks like HTML (rate limit page)
          if (data.trim().startsWith('<') || data.includes('Too many requests')) {
            reject(new Error('Too many requests - rate limited (HTML response)'));
            return;
          }

          const jsonData = JSON.parse(data);

          if (res.statusCode !== 200) {
            reject(new Error(`HTTP ${res.statusCode}: ${jsonData.reason || 'API error'}`));
            return;
          }

          if (jsonData.error) {
            reject(new Error(jsonData.reason || 'API error'));
            return;
          }

          resolve(jsonData);
        } catch (parseError) {
          // If JSON parsing fails, check if it's a rate limit response
          if (data.includes('Too many requests') || data.includes('rate limit')) {
            reject(new Error('Too many requests - rate limited'));
          } else {
            reject(new Error(`JSON parse error: ${parseError.message}`));
          }
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Request error: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
};

/**
 * Get country flag emoji from country code
 * @param {string} countryCode - Two-letter country code
 * @returns {string} Flag emoji
 */
const getCountryFlag = (countryCode) => {
  if (!countryCode || countryCode === 'UN' || countryCode === 'LO') {
    return '🌍';
  }
  
  // Convert country code to flag emoji
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt());
  
  return String.fromCodePoint(...codePoints);
};

/**
 * Batch process multiple IPs with rate limiting
 * @param {string[]} ips - Array of IP addresses
 * @returns {Promise<Object>} Object with IP as key and geo data as value
 */
const batchGetCountriesFromIPs = async (ips) => {
  console.log(`Starting batch geolocation lookup for ${ips.length} IPs`);
  const results = {};
  
  // Remove duplicates and clean IPs
  const uniqueIPs = [...new Set(ips.map(ip => {
    if (ip && ip.includes(',')) {
      return ip.split(',')[0].trim();
    }
    return ip;
  }))].filter(ip => ip && ip.trim() !== '');

  console.log(`Processing ${uniqueIPs.length} unique IPs`);

  for (let i = 0; i < uniqueIPs.length; i++) {
    const ip = uniqueIPs[i];
    console.log(`Processing IP ${i + 1}/${uniqueIPs.length}: ${ip}`);

    try {
      const countryInfo = await getCountryFromIP(ip);
      results[ip] = countryInfo;
    } catch (error) {
      console.warn(`Failed to get geolocation for IP ${ip}:`, error);
      results[ip] = {
        country: 'Unknown',
        country_code: 'UN',
        city: 'Unknown',
        region: 'Unknown',
        timezone: 'Unknown',
        isp: 'Unknown',
        flag: '🌍',
        error: true,
        errorMessage: error.message
      };
    }
  }

  console.log('Batch geolocation lookup completed');
  return results;
};

module.exports = {
  getCountryFromIP,
  batchGetCountriesFromIPs,
  getCountryFlag
};
