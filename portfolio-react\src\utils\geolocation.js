// IP Geolocation utility service
// Using multiple services with fallback

const GEOLOCATION_CACHE = new Map();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Get country information for an IP address
 * @param {string} ip - IP address to lookup
 * @returns {Promise<Object>} Country information object
 */
export const getCountryFromIP = async (ip) => {
  // Check if IP is localhost or private
  if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
    return {
      country: 'Local',
      country_code: 'LO',
      city: 'Localhost',
      region: 'Local',
      flag: '🏠',
      error: false
    };
  }

  // Check cache first
  const cacheKey = ip;
  const cached = GEOLOCATION_CACHE.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    return cached.data;
  }

  try {
    console.log(`Getting location for IP: ${ip}`);

    // Create timeout controller
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    // Try ipapi.co first (most reliable)
    const response = await fetch(`https://ipapi.co/${ip}/json/`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Handle API errors
    if (data.error) {
      throw new Error(data.reason || 'API error');
    }

    const countryInfo = {
      country: data.country_name || 'Unknown',
      country_code: data.country_code || 'UN',
      city: data.city || 'Unknown',
      region: data.region || 'Unknown',
      timezone: data.timezone || 'Unknown',
      isp: data.org || 'Unknown',
      flag: getCountryFlag(data.country_code),
      error: false
    };

    // Cache the result
    GEOLOCATION_CACHE.set(cacheKey, {
      data: countryInfo,
      timestamp: Date.now()
    });

    console.log(`Successfully got location for ${ip}:`, countryInfo);
    return countryInfo;

  } catch (error) {
    console.warn(`Geolocation lookup failed for IP ${ip}:`, error.message);

    // Return fallback data
    const fallbackData = {
      country: 'Unknown',
      country_code: 'UN',
      city: 'Unknown',
      region: 'Unknown',
      timezone: 'Unknown',
      isp: 'Unknown',
      flag: '🌍',
      error: true,
      errorMessage: error.message
    };

    // Cache the fallback to avoid repeated failed requests
    GEOLOCATION_CACHE.set(cacheKey, {
      data: fallbackData,
      timestamp: Date.now()
    });

    return fallbackData;
  }
};

/**
 * Get country flag emoji from country code
 * @param {string} countryCode - Two-letter country code
 * @returns {string} Flag emoji
 */
const getCountryFlag = (countryCode) => {
  if (!countryCode || countryCode.length !== 2) return '🌍';
  
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt());
  
  return String.fromCodePoint(...codePoints);
};

/**
 * Batch lookup multiple IPs
 * @param {string[]} ips - Array of IP addresses
 * @returns {Promise<Object>} Object with IP as key and country info as value
 */
export const batchGetCountriesFromIPs = async (ips) => {
  const uniqueIPs = [...new Set(ips)];
  const results = {};

  console.log(`Starting geolocation lookup for ${uniqueIPs.length} unique IPs:`, uniqueIPs);

  // Process IPs sequentially to avoid rate limiting and CORS issues
  for (let i = 0; i < uniqueIPs.length; i++) {
    const ip = uniqueIPs[i];
    console.log(`Processing IP ${i + 1}/${uniqueIPs.length}: ${ip}`);

    try {
      const countryInfo = await getCountryFromIP(ip);
      results[ip] = countryInfo;

      // Add longer delay between requests to be more conservative with rate limits
      if (i < uniqueIPs.length - 1) { // Don't delay after the last request
        console.log('Waiting 1 second before next request...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.warn(`Failed to get geolocation for IP ${ip}:`, error);
      // Set fallback data for failed IP
      results[ip] = {
        country: 'Unknown',
        country_code: 'UN',
        city: 'Unknown',
        region: 'Unknown',
        timezone: 'Unknown',
        isp: 'Unknown',
        flag: '🌍',
        error: true,
        errorMessage: error.message
      };
    }
  }

  console.log('Geolocation lookup completed. Results:', results);
  return results;
};

/**
 * Clear the geolocation cache
 */
export const clearGeolocationCache = () => {
  GEOLOCATION_CACHE.clear();
};

/**
 * Get cache statistics
 * @returns {Object} Cache statistics
 */
export const getCacheStats = () => {
  return {
    size: GEOLOCATION_CACHE.size,
    entries: Array.from(GEOLOCATION_CACHE.keys())
  };
};
