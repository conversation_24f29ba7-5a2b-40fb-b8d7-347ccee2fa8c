import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { useVisitorTracking } from '../hooks/useVisitorTracking';

console.log('API URL:', process.env.REACT_APP_API_URL);

const LONG_PRESS_DURATION = 5000; // 5 seconds

const Header = () => {
  const { ref } = useVisitorTracking('header', {
    threshold: 0.8,
    minDuration: 1,
    trackOnMount: true
  });

  const [showAdminModal, setShowAdminModal] = useState(false);
  const [adminEmail, setAdminEmail] = useState("");
  const [adminPassword, setAdminPassword] = useState("");
  const [adminLoginMessage, setAdminLoginMessage] = useState("");
  const longPressTimer = useRef(null);
  const longPressTriggered = useRef(false);
  const navigate = useNavigate();

  // Start long press timer
  const startLongPress = (e) => {
    e.preventDefault();
    longPressTriggered.current = false;
    longPressTimer.current = setTimeout(() => {
      setShowAdminModal(true);
      longPressTriggered.current = true;
    }, LONG_PRESS_DURATION);
  };

  // Cancel long press timer
  const cancelLongPress = (e) => {
    clearTimeout(longPressTimer.current);
    if (!longPressTriggered.current && e.type === 'click') {
      // Only reload if not a long press
      window.location.reload();
    }
  };

  // --- Admin Modal Login Handler ---
  const handleAdminLogin = async (e) => {
    e.preventDefault();
    setAdminLoginMessage("");

    // Wake up backend before attempting login
    setAdminLoginMessage('Connecting to server...');
    await preemptiveWakeup();

    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/admin/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: adminEmail, password: adminPassword })
      });
      const data = await response.json();
      if (data.token) {
        localStorage.setItem('token', data.token);
        setAdminLoginMessage('Login successful! Redirecting...');
        setTimeout(() => {
          setShowAdminModal(false);
          navigate('/admin/dashboard');
        }, 1000);
      } else {
        setAdminLoginMessage(data.message || 'Login failed');
      }
    } catch (err) {
      setAdminLoginMessage('Network error');
    }
  };

  return (
    <header ref={ref}>
      <div className="logo">
        <a
          href="/"
          onMouseDown={startLongPress}
          onMouseUp={cancelLongPress}
          onMouseLeave={cancelLongPress}
          onTouchStart={startLongPress}
          onTouchEnd={cancelLongPress}
          onClick={cancelLongPress}
        >
          <img src="/logo.PNG" alt="Logo" className="logo-img" />
        </a>
      </div>
      <a 
        href="https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93" 
        className="cv-button"
        target="_blank"
        rel="noopener noreferrer"
      >
        Get CV
      </a>
      {showAdminModal && (
        <div className="admin-modal-overlay" onClick={() => setShowAdminModal(false)}>
          <div className="admin-modal" onClick={e => e.stopPropagation()}>
            <button className="admin-modal-close" onClick={() => setShowAdminModal(false)}>&times;</button>
            <h2 className="admin-modal-title">Admin Login</h2>
            <form className="admin-modal-form" onSubmit={handleAdminLogin} autoComplete="off">
              <label htmlFor="admin-email">Email</label>
              <input type="email" id="admin-email" name="email" autoComplete="username" required value={adminEmail} onChange={e => setAdminEmail(e.target.value)} />
              <label htmlFor="admin-password">Password</label>
              <input type="password" id="admin-password" name="password" autoComplete="current-password" required value={adminPassword} onChange={e => setAdminPassword(e.target.value)} />
              <button type="submit" className="admin-modal-submit">Login</button>
            </form>
            {adminLoginMessage && <div style={{ marginTop: 8, color: adminLoginMessage.includes('success') ? 'green' : 'red' }}>{adminLoginMessage}</div>}
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
